# Moodplay 独立 Nginx 配置部署指南

## 方案说明

采用独立配置文件方案，与客户的 Nginx 配置完全分离：
- 客户配置：`/etc/nginx/conf.d/default.conf` (保持不变)
- 你的配置：`/etc/nginx/conf.d/moodplay.conf` (新增)

## 配置文件说明

### 文件：`moodplay.conf`
- **位置**：`/etc/nginx/conf.d/moodplay.conf`
- **作用**：处理 `moodplay.top` 域名的所有请求
- **端口**：复用客户的 8090 端口（SSL）

### 服务路由

| 路径 | 目标服务 | 说明 |
|------|----------|------|
| `https://moodplay.top/admin/` | Admin Frontend (5173) | React 管理后台 |
| `https://moodplay.top/api/` | Backend API (3001) | Node.js API |
| `https://moodplay.top/` | 重定向到 `/admin/` | 根路径重定向 |
| `https://moodplay.top/health` | Nginx 健康检查 | 返回 "healthy" |

## 部署步骤

### 1. 准备配置文件

```bash
# 在本地项目目录
# 配置文件已生成：moodplay.conf
```

### 2. 上传到生产服务器

```bash
# 将 moodplay.conf 上传到生产服务器
scp moodplay.conf user@server:/tmp/
```

### 3. 安装配置文件

```bash
# 在生产服务器上
sudo cp /tmp/moodplay.conf /etc/nginx/conf.d/
sudo chown root:root /etc/nginx/conf.d/moodplay.conf
sudo chmod 644 /etc/nginx/conf.d/moodplay.conf
```

### 4. SSL 证书配置

**重要**：需要为 `moodplay.top` 配置 SSL 证书

#### 选项 A：使用 Let's Encrypt
```bash
# 安装 certbot
sudo apt install certbot python3-certbot-nginx

# 申请证书
sudo certbot certonly --nginx -d moodplay.top

# 证书路径通常在：
# /etc/letsencrypt/live/moodplay.top/fullchain.pem
# /etc/letsencrypt/live/moodplay.top/privkey.pem
```

#### 选项 B：使用现有证书
如果已有证书，需要更新 `moodplay.conf` 中的路径：
```nginx
ssl_certificate /path/to/your/moodplay.top.pem;
ssl_certificate_key /path/to/your/moodplay.top.key;
```

### 5. 测试配置

```bash
# 测试 Nginx 配置语法
sudo nginx -t

# 查看完整配置（包含所有文件）
sudo nginx -T | grep -A 20 "moodplay.top"
```

### 6. 应用配置

```bash
# 重新加载 Nginx 配置
sudo nginx -s reload
```

### 7. 启动 Moodplay 服务

```bash
# 在 moodplay 项目目录
docker-compose -f docker-compose.prod.yml up -d
```

## 验证部署

### 检查服务状态

```bash
# 检查 Nginx 状态
sudo systemctl status nginx

# 检查 Docker 容器
docker-compose -f docker-compose.prod.yml ps

# 检查端口监听
netstat -tlnp | grep -E "(3001|5173|8090)"
```

### 测试访问

```bash
# 测试管理后台
curl -k https://moodplay.top/admin/

# 测试 API
curl -k https://moodplay.top/api/health

# 测试健康检查
curl -k https://moodplay.top/health

# 测试根路径重定向
curl -I https://moodplay.top/
```

## 配置特点

### 1. 完全独立
- 与客户配置文件分离
- 不影响客户现有服务
- 可以独立更新和维护

### 2. 资源共享
- 复用客户的 Nginx 进程
- 共享 8090 端口
- 基于 `server_name` 区分请求

### 3. 安全配置
- 独立的 SSL 证书
- 完整的安全头设置
- 适当的超时配置

### 4. 性能优化
- 静态资源缓存
- 连接池配置
- 负载均衡设置

## 维护操作

### 更新配置

```bash
# 1. 修改本地 moodplay.conf
# 2. 上传到服务器
scp moodplay.conf user@server:/tmp/
sudo cp /tmp/moodplay.conf /etc/nginx/conf.d/

# 3. 测试并重新加载
sudo nginx -t && sudo nginx -s reload
```

### 查看日志

```bash
# Nginx 访问日志
sudo tail -f /var/log/nginx/access.log | grep moodplay

# Nginx 错误日志
sudo tail -f /var/log/nginx/error.log

# Moodplay 服务日志
docker-compose -f docker-compose.prod.yml logs -f
```

### 故障排除

1. **502 Bad Gateway**
   - 检查后端服务是否运行
   - 检查端口映射是否正确

2. **SSL 证书错误**
   - 检查证书文件路径
   - 验证证书是否包含正确域名

3. **配置冲突**
   - 使用 `nginx -T` 查看完整配置
   - 检查是否有重复的 `server_name`

## 优势总结

✅ **独立性**：不影响客户配置
✅ **维护性**：可以独立版本控制
✅ **安全性**：配置隔离，降低风险
✅ **效率性**：共享 Nginx 进程，节省资源
✅ **灵活性**：可以随时调整配置
