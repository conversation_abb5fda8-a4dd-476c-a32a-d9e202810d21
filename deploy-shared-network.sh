#!/bin/bash

# Moodplay 共享网络部署脚本
set -e

echo "🔍 检查客户 Docker 网络..."

# 查看所有网络
echo "当前 Docker 网络列表："
docker network ls

echo ""
echo "🔍 查找客户的网络..."

# 查找可能的客户网络
CUSTOMER_NETWORKS=$(docker network ls --format "table {{.Name}}" | grep -E "(default|customer|client)" | grep -v "bridge\|host\|none")

if [ -z "$CUSTOMER_NETWORKS" ]; then
    echo "❌ 未找到明显的客户网络，请手动确认网络名称"
    echo "💡 提示：查看客户 docker-compose.yml 所在目录名，网络名通常是 {目录名}_default"
    echo ""
    echo "📋 所有网络："
    docker network ls
    echo ""
    read -p "请输入客户的网络名称: " CUSTOMER_NETWORK
else
    echo "🎯 找到可能的客户网络："
    echo "$CUSTOMER_NETWORKS"
    echo ""
    read -p "请选择客户的网络名称 (或手动输入): " CUSTOMER_NETWORK
fi

if [ -z "$CUSTOMER_NETWORK" ]; then
    echo "❌ 错误: 必须指定客户网络名称"
    exit 1
fi

echo "✅ 使用客户网络: $CUSTOMER_NETWORK"

# 更新 docker-compose.prod.yml 中的网络名称
echo "📝 更新 docker-compose.prod.yml..."
sed -i.backup "s/name: customer_default.*/name: $CUSTOMER_NETWORK/" docker-compose.prod.yml

echo "✅ 已更新网络配置"

# 停止现有服务
echo "🛑 停止现有 Moodplay 服务..."
docker-compose -f docker-compose.prod.yml down

# 重新启动服务
echo "🚀 启动 Moodplay 服务（共享网络模式）..."
docker-compose -f docker-compose.prod.yml up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "📋 检查服务状态..."
docker-compose -f docker-compose.prod.yml ps

# 测试网络连接
echo "🔍 测试网络连接..."
echo "测试从客户 nginx 容器连接到你的后端..."

if docker exec nginx-proxy wget -qO- http://moodplay-backend:3000/api/health-check 2>/dev/null; then
    echo "✅ 网络连接成功！"
else
    echo "❌ 网络连接失败，请检查："
    echo "1. 客户网络名称是否正确"
    echo "2. 你的服务是否正常启动"
    echo "3. 后端健康检查路径是否正确"
fi

# 更新 nginx 配置
echo "📁 更新 Nginx 配置..."
cp moodplay.conf ./nginx.config.d/

# 测试 nginx 配置
echo "🔍 测试 Nginx 配置..."
if docker exec nginx-proxy nginx -t; then
    echo "✅ Nginx 配置测试通过"
    
    # 重启 nginx
    echo "🔄 重启 Nginx..."
    docker-compose restart nginx
    
    echo "✅ 部署完成！"
    echo ""
    echo "🎯 测试访问："
    echo "curl -k https://moodplay.top/api/health-check"
    echo "curl -k https://moodplay.top/admin/"
    
else
    echo "❌ Nginx 配置测试失败"
    exit 1
fi
