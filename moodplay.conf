# Moodplay 项目 Nginx 配置
# 文件位置: /etc/nginx/conf.d/moodplay.conf

# === MOODPLAY UPSTREAM 配置 ===
# Moodplay Backend API 服务
upstream moodplay_backend {
    server moodplay-backend:3000;  # 使用容器名和内部端口
    least_conn;
    keepalive 16;
}

# Moodplay Admin Frontend 服务
upstream moodplay_admin {
    server moodplay-admin-frontend:80;  # 使用容器名和内部端口
    least_conn;
    keepalive 8;
}

# === MOODPLAY SERVER 配置 ===
server {
    listen 8090 ssl;
    listen [::]:8090 ssl;
    http2 on;
    server_name moodplay.top *.moodplay.top;

    # SSL 证书配置 (需要替换为实际的 moodplay.top 证书)
    ssl_certificate /opt/cert/moodplay.top.pem;
    ssl_certificate_key /opt/cert/moodplay.top.privkey.pem;

    # SSL 安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers off;
    ssl_ciphers ECDH+AESGCM:ECDH+CHACHA20:DH+AESGCM:ECDH+AES256:DH+AES256:ECDH+AES128:DH+AES:RSA+AESGCM:RSA+AES:!aNULL:!MD5:!DSS;

    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Permissions-Policy "interest-cohort=()" always;

    # === 管理后台路由 ===
    # 访问路径: https://moodplay.top/admin/
    location /admin/ {
        proxy_pass http://moodplay_admin/admin/;
        
        # 标准代理头
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $server_name;
        proxy_set_header X-Forwarded-Port $server_port;

        # 超时设置
        proxy_read_timeout 60s;
        proxy_connect_timeout 10s;
        proxy_send_timeout 60s;
    }

    # 前端静态资源优化
    location ~* /admin/.*\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://moodplay_admin;
        
        # 缓存设置
        expires 1y;
        add_header Cache-Control "public, immutable";
        
        # 代理头
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # === API 路由 ===
    # 访问路径: https://moodplay.top/api/
    location /api/ {
        # 直接代理，保持 /api/ 前缀
        proxy_pass http://moodplay_backend;
        
        # 标准代理头
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $server_name;
        proxy_set_header X-Forwarded-Port $server_port;

        # API 超时设置 (适合后端处理时间)
        proxy_read_timeout 120s;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;

        # 错误处理
        proxy_next_upstream error timeout http_500 http_502 http_503 http_504;
    }

    # === 根路径处理 ===
    # 访问 https://moodplay.top/ 重定向到管理后台
    location = / {
        return 301 https://$server_name/admin/;
    }

    # === 健康检查路由 ===
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # === 其他路径处理 ===
    location / {
        # 未匹配的路径返回 404
        return 404;
    }
}
