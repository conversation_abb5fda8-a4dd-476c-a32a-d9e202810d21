#!/bin/bash

# Moodplay Nginx 配置部署脚本
set -e

echo "🚀 部署 Moodplay Nginx 配置..."

# 检查必要文件
if [ ! -f "moodplay.conf" ]; then
    echo "❌ 错误: moodplay.conf 文件不存在"
    exit 1
fi

# 检查是否在生产服务器上运行
if [ ! -d "/etc/nginx/conf.d" ]; then
    echo "❌ 错误: 未找到 /etc/nginx/conf.d 目录，请确认在正确的服务器上运行"
    exit 1
fi

echo "📋 部署信息:"
echo "- 配置文件: moodplay.conf"
echo "- 目标位置: /etc/nginx/conf.d/moodplay.conf"
echo "- 域名: moodplay.top"
echo ""

# 确认部署
read -p "确认部署? (y/N): " confirm
if [[ $confirm != [yY] ]]; then
    echo "❌ 部署已取消"
    exit 0
fi

# 备份现有配置（如果存在）
if [ -f "/etc/nginx/conf.d/moodplay.conf" ]; then
    echo "📦 备份现有配置..."
    sudo cp /etc/nginx/conf.d/moodplay.conf /etc/nginx/conf.d/moodplay.conf.backup.$(date +%Y%m%d_%H%M%S)
fi

# 复制配置文件
echo "📁 复制配置文件..."
sudo cp moodplay.conf /etc/nginx/conf.d/
sudo chown root:root /etc/nginx/conf.d/moodplay.conf
sudo chmod 644 /etc/nginx/conf.d/moodplay.conf

# 测试配置
echo "🔍 测试 Nginx 配置..."
if sudo nginx -t; then
    echo "✅ Nginx 配置测试通过"
else
    echo "❌ Nginx 配置测试失败，请检查配置文件"
    exit 1
fi

# 重新加载配置
echo "🔄 重新加载 Nginx 配置..."
sudo nginx -s reload

echo "✅ Moodplay Nginx 配置部署完成！"
echo ""
echo "📋 接下来的步骤:"
echo "1. 配置 SSL 证书 (如果还未配置)"
echo "2. 启动 Moodplay Docker 服务"
echo "3. 测试访问 https://moodplay.top/admin/"
echo ""
echo "🔧 有用的命令:"
echo "# 查看配置"
echo "sudo nginx -T | grep -A 20 'moodplay.top'"
echo ""
echo "# 查看日志"
echo "sudo tail -f /var/log/nginx/access.log | grep moodplay"
echo ""
echo "# 测试访问"
echo "curl -k https://moodplay.top/health"
